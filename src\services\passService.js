// src/services/passService.js
import { nhost } from './nhost';
import { ensureValidSession } from './auth';
import { GraphQLServiceError, PassServiceError } from './errors';

// GraphQL queries
const GET_USER_ACTIVE_PASS = `
  query GetUserActivePass($userId: uuid!) {
    user_wallet(where: { user_id: { _eq: $userId } }) {
      user_id
      active_pass_id
    }
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        _or: [
          { end_date: { _is_null: true } },
          { end_date: { _gte: "now()" } }
        ]
      }
    ) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      hours_used
      created_at
      updated_at
      pass_type {
        id
        name
        tier
        category
        monthly_price
        hours_included
        is_unlimited
        slow_generation_limit
        fast_generation_limit
        features
        created_at
        updated_at
      }
    }
  }
`;

const GET_AVAILABLE_PASSES = `
  query GetAvailablePasses {
    pass_types(where: { is_active: { _eq: true } }, order_by: { sort_order: asc }) {
      id
      name
      tier
      category
      monthly_price
      hours_included
      is_unlimited
      slow_generation_limit
      fast_generation_limit
      provider_price_id
      features
      is_active
      sort_order
      created_at
      updated_at
    }
  }
`;

/**
 * Get user's current active pass
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - User's active pass details
 */
async function getUserActivePass(userId) {
  try {
    if (!userId) {
      throw new PassServiceError('User ID is required');
    }

    // Ensure we have a valid session before making the request
    const sessionResult = await ensureValidSession();
    if (!sessionResult.success) {
      throw new GraphQLServiceError('Authentication required', [], sessionResult.error);
    }

    const { data, error } = await nhost.graphql.request(GET_USER_ACTIVE_PASS, { userId });
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch user pass', error.response?.errors, error);
    }
    
    if (!data?.user_wallet?.[0]) {
      return { hasActivePass: false };
    }

    const wallet = data.user_wallet[0];
    const activePasses = data.user_passes || [];
    const activePass = activePasses.find(pass =>
      pass.status === 'active' &&
      (!pass.end_date || new Date(pass.end_date) > new Date())
    );

    return {
      hasActivePass: !!activePass,
      activePass: activePass || null,
      wallet: wallet
    };
  } catch (error) {
    console.error('[PassService] Error fetching user pass:', error);
    if (error instanceof GraphQLServiceError || error instanceof PassServiceError) {
      throw error;
    }
    throw new PassServiceError(`Failed to get user active pass: ${error.message}`, error);
  }
}

/**
 * Get all available pass types
 * @returns {Promise<Array>} - Available pass types
 */
async function getAvailablePasses() {
  try {
    // Ensure we have a valid session before making the request
    const sessionResult = await ensureValidSession();
    if (!sessionResult.success) {
      throw new GraphQLServiceError('Authentication required', [], sessionResult.error);
    }

    const { data, error } = await nhost.graphql.request(GET_AVAILABLE_PASSES);
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch available passes', error.response?.errors, error);
    }
    
    return data?.pass_types || [];
  } catch (error) {
    console.error('[PassService] Error fetching available passes:', error);
    if (error instanceof GraphQLServiceError) {
      throw error;
    }
    throw new PassServiceError(`Failed to get available passes: ${error.message}`, error);
  }
}

export const PassService = {
  getUserActivePass,
  getAvailablePasses
};
