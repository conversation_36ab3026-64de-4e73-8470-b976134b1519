// Mock implementation for tests
const nhost = require('../../_utils/nhost');
const { TransactionType, DeductionSource, CreditErrorType } = require('../types');

// Mock wallet data
const mockUserWallet = {
  id: 'wallet-id',
  user_id: 'user-id',
  credits_balance: 1000,
  fast_credits_balance: 500,
  last_free_credit_date: null,
  active_pass_id: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

// Mock for handleCreditTransaction
function mockHandleCreditTransaction(req, res) {
  const { userId, amount, transactionType, referenceId, bundleId, deductionSource } = req.body;
  
  // Validate amount
  if (amount <= 0 && transactionType !== TransactionType.CREDIT_USAGE) {
    return res.status(400).json({
      success: false,
      message: 'Transaction amount must be positive',
      type: CreditErrorType.INVALID_AMOUNT
    });
  }

  if (amount >= 0 && transactionType === TransactionType.CREDIT_USAGE) {
    return res.status(400).json({
      success: false,
      message: 'Credit usage amount must be negative',
      type: CreditErrorType.INVALID_AMOUNT
    });
  }

  // Check if wallet exists
  if (userId !== mockUserWallet.user_id) {
    return res.status(404).json({
      success: false,
      message: 'User wallet not found',
      type: CreditErrorType.WALLET_NOT_FOUND
    });
  }

  // Calculate new balances
  let balanceAfter = mockUserWallet.credits_balance;
  let fastBalanceAfter = mockUserWallet.fast_credits_balance;
  let hasUnlimitedUsage = false;

  // Handle different transaction types
  switch (transactionType) {
    case TransactionType.CREDIT_USAGE:
      if (deductionSource === DeductionSource.FAST) {
        if (mockUserWallet.fast_credits_balance + amount < 0) {
          return res.status(400).json({
            success: false,
            message: 'Insufficient fast credits balance',
            type: CreditErrorType.INSUFFICIENT_BALANCE
          });
        }
        fastBalanceAfter = mockUserWallet.fast_credits_balance + amount;
      } else {
        if (mockUserWallet.credits_balance + amount < 0) {
          return res.status(400).json({
            success: false,
            message: 'Insufficient credits balance',
            type: CreditErrorType.INSUFFICIENT_BALANCE
          });
        }
        balanceAfter = mockUserWallet.credits_balance + amount;
      }
      break;
      
    case TransactionType.CREDIT_PURCHASE:
      if (!bundleId) {
        return res.status(400).json({
          success: false,
          message: 'Bundle ID is required for credit purchases',
          type: CreditErrorType.BUNDLE_NOT_FOUND
        });
      }
      
      if (bundleId !== 'bundle-id') {
        return res.status(404).json({
          success: false,
          message: 'Credit bundle not found',
          type: CreditErrorType.BUNDLE_NOT_FOUND
        });
      }
      
      balanceAfter = mockUserWallet.credits_balance + amount;
      break;
      
    default:
      return res.status(400).json({
        success: false,
        message: 'Invalid transaction type',
        type: CreditErrorType.INVALID_TRANSACTION_TYPE
      });
  }

  // Return success response
  return res.status(200).json({
    success: true,
    message: 'Transaction completed successfully',
    transactionId: 'mocked-uuid',
    balanceAfter,
    fastBalanceAfter,
    hasUnlimitedUsage
  });
}

module.exports = {
  mockHandleCreditTransaction
};

