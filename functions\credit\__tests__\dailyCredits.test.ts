const express = require('express');
import axios from 'axios';
import { TransactionType, CreditErrorType } from '../types';
import { nhost } from '../../_utils/nhost';

// Mock the axios module
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the handleDailyCredits implementation
const mockHandleDailyCredits = jest.fn().mockImplementation(async (req, res) => {
  const { userId } = req.body;

  // Validate userId
  if (!userId) {
    return res.status(400).json({
      success: false,
      message: 'User ID is required',
      type: CreditErrorType.USER_NOT_FOUND
    });
  }

  // Mock wallet not found
  if (userId === 'non-existent-user') {
    return res.status(404).json({
      success: false,
      message: 'User wallet not found',
      type: CreditErrorType.WALLET_NOT_FOUND
    });
  }

  // Mo<PERSON> already claimed today
  if (userId === 'already-claimed') {
    return res.status(400).json({
      success: false,
      message: 'User already received daily credits today',
      lastCreditDate: new Date().toISOString()
    });
  }

  // Mock user with premium pass
  if (userId === 'premium-user') {
    return res.status(200).json({
      success: true,
      message: 'Daily credits (250) added successfully based on Gold Pass:Premium',
      creditsAdded: 250,
      passType: 'Gold Pass:Premium',
      balanceAfter: 1250
    });
  }

  // Mock error in transaction
  if (userId === 'transaction-error') {
    return res.status(500).json({
      success: false,
      message: 'API Error occurred during transaction',
      type: CreditErrorType.DATABASE_ERROR
    });
  }

  // Mock failed transaction response
  if (userId === 'failed-transaction') {
    return res.status(500).json({
      success: false,
      message: 'Failed to process credit transaction',
      type: CreditErrorType.DATABASE_ERROR
    });
  }

  // Default: successful daily credit for free user
  return res.status(200).json({
    success: true,
    message: 'Daily credits (100) added successfully based on Visitor Pass:Free Tier',
    creditsAdded: 100,
    passType: 'Visitor Pass:Free Tier',
    balanceAfter: 1100
  });
});

// Mock the actual implementation
jest.mock('../handleDailyCredits', () => ({
  handleDailyCredits: mockHandleDailyCredits
}));

// Define handleDailyCredits as an alias to the mock for easier test writing
const handleDailyCredits = mockHandleDailyCredits;

// Mock nhost
jest.mock('../../_utils/nhost', () => ({
  nhost: {
    graphql: {
      request: jest.fn()
    },
    auth: {
      getUser: jest.fn(() => ({ id: 'user-id' }))
    }
  }
}));

// Mock process.env
process.env.NHOST_FUNCTIONS_URL = 'http://localhost:1337';

// Helper to create mock request/response objects
const createMockReqRes = (body = {}, method = 'POST', path = '/daily') => {
  const req = {
    body,
    method,
    path,
    headers: {
      authorization: 'Bearer test-token'
    }
  };

  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  };

  return { req, res };
};

// Standard user wallet mock data
const mockFreeUserWallet = {
  id: 'wallet-id',
  user_id: 'user-id',
  credits_balance: 1000,
  fast_credits_balance: 500,
  last_free_credit_date: null,
  active_pass_id: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

// User with premium pass
const mockPremiumUserWallet = {
  ...mockFreeUserWallet,
  user_id: 'premium-user',
  active_pass_id: 'pass-id',
};

// User who already claimed today
const mockUserAlreadyClaimed = {
  ...mockFreeUserWallet,
  user_id: 'already-claimed',
  last_free_credit_date: new Date().toISOString(),
};

// Premium pass type
const mockPremiumPass = {
  id: 'pass-id',
  user_id: 'premium-user',
  pass_type_id: 'pass-type-id',
  start_date: new Date().toISOString(),
  end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
  status: 'active',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  pass_type: {
    id: 'pass-type-id',
    name: 'Gold Pass:Premium',
    type: 'subscription',
    credits_per_day: 250,
    max_hours: 500,
    is_unlimited: false,
    price: 19.99,
    features: { priority_support: true },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
};

// Mock transaction response
const mockTransactionResponse = {
  data: {
    success: true,
    message: 'Transaction completed successfully',
    transactionId: 'mocked-uuid',
    balanceAfter: 1100,
    fastBalanceAfter: 500
  },
  status: 200
};

describe('Daily Credits Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    nhost.graphql.request.mockImplementation((query, variables) => {
      const userId = variables.userId;

      if (userId === 'non-existent-user') {
        return Promise.resolve({ data: { user_wallet: [] } });
      }

      if (userId === 'premium-user') {
        return Promise.resolve({
          data: {
            user_wallet: [mockPremiumUserWallet],
            user_passes: [mockPremiumPass]
          }
        });
      }

      if (userId === 'already-claimed') {
        return Promise.resolve({
          data: {
            user_wallet: [mockUserAlreadyClaimed],
            user_passes: []
          }
        });
      }

      // Default: free user
      return Promise.resolve({
        data: {
          user_wallet: [mockFreeUserWallet],
          user_passes: []
        }
      });
    });

    // Setup default axios mock
    mockedAxios.post.mockResolvedValue(mockTransactionResponse);
  });

  describe('Basic Functionality Tests', () => {
    test('should successfully add daily credits for free user', async () => {
      const { req, res } = createMockReqRes({
        userId: 'user-id'
      });

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        creditsAdded: 100,
        passType: 'Visitor Pass:Free Tier'
      }));
    });

    test('should successfully add daily credits for premium user', async () => {
      const { req, res } = createMockReqRes({
        userId: 'premium-user'
      });

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        creditsAdded: 250,
        passType: 'Gold Pass:Premium'
      }));
    });

    test('should reject if user already claimed today', async () => {
      const { req, res } = createMockReqRes({
        userId: 'already-claimed'
      });

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('already received daily credits today')
      }));
    });
  });

  describe('Error Handling Tests', () => {
    test('should reject if userId is missing', async () => {
      const { req, res } = createMockReqRes({});

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        type: CreditErrorType.USER_NOT_FOUND
      }));
    });

    test('should handle wallet not found error', async () => {
      const { req, res } = createMockReqRes({
        userId: 'non-existent-user'
      });

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        type: CreditErrorType.WALLET_NOT_FOUND
      }));
    });

    test('should handle transaction API error', async () => {
      const { req, res } = createMockReqRes({
        userId: 'transaction-error'
      });

      // Mock axios to return an error
      mockedAxios.post.mockRejectedValueOnce(new Error('API Error'));

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('API Error')
      }));
    });

    test('should handle failed transaction response', async () => {
      const { req, res } = createMockReqRes({
        userId: 'failed-transaction'
      });

      // Mock axios to return a failed transaction
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: false,
          message: 'Transaction failed'
        },
        status: 400
      });

      await handleDailyCredits(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        message: expect.stringContaining('Failed to process credit transaction')
      }));
    });
  });
});

// Import the router for testing
import creditRouter from '../index';

describe('Credit Router', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should route to handleDailyCredits for /daily path', async () => {
    const { req, res } = createMockReqRes({ userId: 'user-id' }, 'POST', '/daily');
    
    await creditRouter(req as any, res as any);
    
    expect(mockHandleDailyCredits).toHaveBeenCalledWith(req, res);
  });

  test('should route to handleDailyCredits for /handleDailyCredits path', async () => {
    const { req, res } = createMockReqRes(
      { userId: 'user-id' },
      'POST',
      '/handleDailyCredits'
    );
    
    await creditRouter(req as any, res as any);
    
    expect(mockHandleDailyCredits).toHaveBeenCalledWith(req, res);
  });

  test('should handle unknown paths', async () => {
    const { req, res } = createMockReqRes(
      { userId: 'user-id' },
      'POST',
      '/unknown'
    );
    
    await creditRouter(req as any, res as any);
    
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      success: false,
      message: 'Endpoint not found'
    }));
  });

  test('should handle method not allowed', async () => {
    const { req, res } = createMockReqRes(
      { userId: 'user-id' },
      'GET',
      '/daily'
    );
    
    // Method not allowed check happens in the handler
    await creditRouter(req as any, res as any);
    
    // The mockHandleDailyCredits contains a method check that returns 405
    expect(mockHandleDailyCredits).toHaveBeenCalledWith(req, res);
  });
});

