// src/pages/MyPlan.jsx
import React, { useState, useCallback, useEffect } from 'react'; // Added useEffect for consistent patterns
import { CardContent, Grid, Divider, Paper, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, CircularProgress, Chip, CardActions, Pagination, Snackbar, Fade, Box } from '@mui/material'; // Added Box
import { useTheme } from '@mui/material/styles';
// Import Nhost hooks and GraphQL
import { useAuthenticationStatus, useUserId } from '@nhost/react'; // Added useUserId for consistency
import { gql, useQuery } from '@apollo/client';
import { nhost } from '../services/nhost'; // Import nhost client for function calls

// Services - Import CreditService for centralized database operations
import { CreditService } from '../services/creditService';

// Design System Components
import {
    Container,
    Stack,
    Heading,
    Text,
    Button,
    Card,
    Alert
} from '../components/design-system/index';




// --- GraphQL Queries (Corrected for Schema v3 & JS Syntax) ---

// Query to get Plans (Subscriptions)
const GET_PLANS_QUERY = gql`
  query GetSubscriptionPlans {
    # Use public_plans table name if schema is public, otherwise adjust
    plans(where: { is_subscription: { _eq: true }, is_active: { _eq: true } }, order_by: { sort_order: asc }) {
      id
      name
      description
      # Correct field name: monthly_price
      monthly_price
      # features field might not exist, remove if not in schema or adjust query
      # features
      provider_price_id # Needed for checkout
      # Add other fields displayed if needed (e.g., grants_fast_credits_monthly)
      grants_fast_credits_monthly
      server_type_access
    }
  }
`;

// Query to get Credit Bundles
const GET_BUNDLES_QUERY = gql`
  query GetCreditBundles {
     # Use public_credit_bundles table name if schema is public
    credit_bundles(where: { is_active: { _eq: true } }, order_by: { sort_order: asc }) {
      id
      name
      description
      price
      credits_awarded
      fast_credits_awarded # Added based on schema v3
      provider_price_id # Needed for checkout
    }
  }
`;

// Keep only the queries for plans and bundles - wallet data will come from CreditService
// This follows the same pattern as Dashboard.jsx

// --- Component ---

function MyPlan() {
  const theme = useTheme();
  const { isLoading: isLoadingAuth, isAuthenticated } = useAuthenticationStatus();
  const userId = useUserId(); // Use consistent hook pattern from Dashboard.jsx

  // --- State Management (following Dashboard.jsx patterns) ---
  const [page, setPage] = useState(1);
  const rowsPerPage = 10;

  // Wallet state management (consistent with Dashboard.jsx)
  const [walletInfo, setWalletInfo] = useState(null);
  const [walletLoading, setWalletLoading] = useState(false);
  const [walletError, setWalletError] = useState(null);

  // Credit transactions state
  const [creditTransactions, setCreditTransactions] = useState([]);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [transactionsError, setTransactionsError] = useState(null);

  // Fetch Plans and Bundles using GraphQL (keep existing pattern for these)
  const { loading: loadingPlans, error: errorPlans, data: dataPlans } = useQuery(GET_PLANS_QUERY);
  const { loading: loadingBundles, error: errorBundles, data: dataBundles } = useQuery(GET_BUNDLES_QUERY);

  // Extract data from GraphQL queries
  const plans = dataPlans?.plans || [];
  const bundles = dataBundles?.credit_bundles || [];

  // Derive current plan/subscription from walletInfo (consistent with Dashboard.jsx)
  const currentPlan = walletInfo?.activePass?.pass_type || null;
  const activeSubscription = walletInfo?.activePass || null;

  // Combined loading state
  const isLoading = isLoadingAuth || walletLoading || loadingPlans || loadingBundles;
  // Combine potential errors - show the first one encountered
  const combinedError = walletError || errorPlans || errorBundles;

  // --- Handlers ---
  // Correct JS syntax for event handler
  const handlePageChange = (event, value) => {
    setPage(value);
  };

   // --- Toast Notification State & Handlers (consistent with Dashboard.jsx) ---
   const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });
   const handleCloseToast = useCallback(() => { setToast(prev => ({ ...prev, open: false })); }, []);
   const showToast = useCallback((message, severity = 'info') => { setToast({ open: true, message, severity }); }, []);

   // --- Fetch wallet information using CreditService (following Dashboard.jsx pattern) ---
   const fetchWalletInfo = useCallback(async () => {
     if (!userId || !isAuthenticated) return;

     setWalletLoading(true);
     setWalletError(null);

     try {
       console.log('[MyPlan] Fetching wallet info using CreditService...');
       const walletData = await CreditService.getUserWalletInfo(userId);
       console.log('[MyPlan] Wallet info retrieved:', walletData);
       setWalletInfo(walletData);
     } catch (error) {
       console.error('[MyPlan] Failed to get wallet info:', error);
       setWalletError(error.message);
     } finally {
       setWalletLoading(false);
     }
   }, [userId, isAuthenticated]);

   // --- Fetch credit transactions using CreditService ---
   const fetchCreditTransactions = useCallback(async () => {
     if (!userId) return;

     setTransactionsLoading(true);
     setTransactionsError(null);

     try {
       console.log('[MyPlan] Fetching credit transactions...');
       const transactions = await CreditService.getCreditTransactions(userId, 50); // Get more for pagination
       console.log('[MyPlan] Credit transactions retrieved:', transactions);
       setCreditTransactions(transactions);
     } catch (error) {
       console.error('[MyPlan] Failed to get credit transactions:', error);
       setTransactionsError(error.message);
     } finally {
       setTransactionsLoading(false);
     }
   }, [userId]);

   // --- Effect to fetch data on component mount (following Dashboard.jsx pattern) ---
   useEffect(() => {
     if (userId && isAuthenticated) {
       fetchWalletInfo();
       fetchCreditTransactions();
     }
   }, [userId, isAuthenticated, fetchWalletInfo, fetchCreditTransactions]);


  // --- Purchase/Subscription Logic (Placeholders - Requires Backend Functions) ---
  const handleCreateCheckout = async (priceId, type) => {
     if (!priceId || !userId) return;
     console.log(`Initiating ${type} checkout for price ${priceId}`);
     showToast(`Initiating ${type}...`, 'info');

     try {
        // Replace 'create-checkout-session' with your actual Nhost function name
        const { res, error } = await nhost.functions.call('create-checkout-session', {
            priceId: priceId,
            type: type,
        });

        if (error) {
            throw new Error(error.message || 'Failed to create checkout session.');
        }

        const checkoutUrl = res?.data?.url;
        if (checkoutUrl) {
            window.location.href = checkoutUrl; // Redirect user
        } else {
            throw new Error('Checkout URL not received from backend.');
        }

     } catch (err) {
        console.error(`Failed to create ${type} checkout:`, err);
        showToast(`Error creating checkout: ${err.message}`, 'error');
     }
  };


  // --- Render Logic (consistent with Dashboard.jsx patterns) ---
  if (isLoadingAuth) {
    return (
      <Container>
        <Stack justifyContent="center" alignItems="center" sx={{ p: 5 }}>
          <CircularProgress />
          <Text>Loading authentication...</Text>
        </Stack>
      </Container>
    );
  }

  if (!isAuthenticated || !userId) {
    return (
      <Container sx={{ py: 4 }}>
        <Alert severity="warning">Please log in to view your plan.</Alert>
      </Container>
    );
  }

  // Show loading only if we haven't loaded any data yet
  if (isLoading && !walletInfo && !dataPlans && !dataBundles) {
     return (
       <Container>
         <Stack justifyContent="center" alignItems="center" sx={{ p: 5 }}>
           <CircularProgress />
           <Text>Loading your plan information...</Text>
         </Stack>
       </Container>
     );
  }

  // Calculate pagination for credit transactions
  const paginatedTransactions = creditTransactions.slice((page - 1) * rowsPerPage, page * rowsPerPage);
  const pageCount = Math.ceil(creditTransactions.length / rowsPerPage);

  return (
    <Container maxWidth="1280px" sx={{ py: 4 }}>
        {/* --- Toast --- */}
        <Snackbar
            open={toast.open} autoHideDuration={4000} onClose={handleCloseToast}
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }} TransitionComponent={Fade}
            sx={{ mt: { xs: '64px', sm: '72px' } }}
        >
            <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%' }}>
                {toast.message}
            </Alert>
        </Snackbar>

        <Stack spacing={5}>
            {/* --- Page Header --- */}
            <Heading level="h1" size="4xl">My Plan & Billing</Heading>

            {/* Display first data loading error */}
            {combinedError && <Alert severity="error">Error loading data: {combinedError.message}</Alert>}

      {/* --- Current Status Card --- */}
      <Card sx={{ mb: 5 }}>
        <CardContent sx={{ p: 3 }}>
          <Heading level="h2" size="xl">Current Status</Heading>
          <Divider sx={{ my: 1.5 }} />
          {/* Show loader inside card only if wallet info is loading */}
          {walletLoading && !walletInfo ? (
              <Stack alignItems="center" sx={{ p: 2 }}>
                  <CircularProgress size={24} />
                  <Text variant="muted" sx={{ mt: 1 }}>Loading wallet information...</Text>
              </Stack>
          ) : (
            <Grid container spacing={2} sx={{ alignItems: 'center', mt: 1 }}>
                <Grid item xs={12} sm={4}>
                  <Text variant="muted" sx={{ mb: 1 }}>Current Plan:</Text>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                      <Heading level="h3" size="lg" color="primary.main" weight={600}>
                          {currentPlan?.name || 'Visitor Pass'}
                      </Heading>
                      {activeSubscription?.status && activeSubscription.status !== 'active' && (
                          <Chip label={activeSubscription.status} size="small" color="warning" />
                      )}
                      {activeSubscription?.status === 'active' && (
                          <Chip label="Active" size="small" color="success" />
                      )}
                  </Stack>
                  {activeSubscription?.current_period_end && (
                      <Text size="xs" variant="muted" sx={{ display: 'block' }}>
                          {/* Ensure timestamp conversion is correct (seconds vs ms) */}
                          Renews/Expires on: {new Date(activeSubscription.current_period_end * 1000).toLocaleDateString()}
                      </Text>
                  )}
                </Grid>
                 <Grid item xs={6} sm={4} sx={{ textAlign: { sm: 'center' } }}>
                    <Text variant="muted" sx={{ mb: 1 }}>General Credits:</Text>
                    <Heading level="h3" size="xl" weight={600}>
                        {walletInfo?.creditsBalance ?? '-'}
                    </Heading>
                 </Grid>
                 <Grid item xs={6} sm={4} sx={{ textAlign: { sm: 'right' } }}>
                    <Text variant="muted" sx={{ mb: 1 }}>Fast Credits:</Text>
                    <Heading level="h3" size="xl" weight={600}>
                        {walletInfo?.fastCreditsBalance ?? 0}
                    </Heading>
                 </Grid>
                 <Grid item xs={12} sx={{ textAlign: 'right', pt: 1 }}>
                     <Text size="xs" variant="muted" sx={{ display: 'block' }}>
                        {walletInfo?.userWallet?.user_id || 'User ID not available'}
                     </Text>
                 </Grid>
            </Grid>
          )}
          {/* TODO: Add button to manage subscription if active */}
          {activeSubscription && (
              <Box sx={{mt: 2, textAlign:'right'}}>
                  <Button variant="outlined" size="small" disabled>Manage Subscription (WIP)</Button>
              </Box>
          )}
        </CardContent>
      </Card>

            {/* --- Subscription Plans Section --- */}
            <Stack spacing={2}>
                <Heading level="h2" size="2xl">Subscription Plans</Heading>
      {loadingPlans && <CircularProgress size={24} />}
      <Grid container spacing={3} sx={{ mb: 5 }}>
        {plans.map((plan) => (
          <Grid item xs={12} md={6} lg={4} key={plan.id}>
            <Card
                variant={currentPlan?.id === plan.id ? "outlined" : "default"}
                sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    border: currentPlan?.id === plan.id ? `2px solid ${theme.palette.primary.main}` : undefined
                }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Heading level="h3" size="lg" sx={{ mb: 1 }}>{plan.name}</Heading>
                <Stack direction="row" alignItems="baseline" spacing={0.5} sx={{ my: 1 }}>
                    <Heading level="h2" size="2xl">${plan.monthly_price?.toFixed(2)}</Heading>
                    <Text size="sm" variant="muted">/month</Text>
                </Stack>
                <Text variant="muted" sx={{ mb: 2, minHeight: '40px' }}>{plan.description}</Text>
                <Text size="xs" variant="muted" sx={{ display: 'block' }}>Access: {plan.server_type_access}</Text>
                {plan.grants_fast_credits_monthly > 0 && (
                    <Text size="xs" variant="muted" sx={{ display: 'block' }}>
                        Fast Credits: {plan.grants_fast_credits_monthly}/month
                    </Text>
                )}
              </CardContent>
              <CardActions sx={{ px: 2, pb: 2, mt: 'auto' }}>
                <Button
                    variant="contained"
                    fullWidth
                    disabled={currentPlan?.id === plan.id || !plan.provider_price_id || walletLoading}
                    onClick={() => handleCreateCheckout(plan.provider_price_id, 'subscription')}
                >
                  {currentPlan?.id === plan.id ? 'Current Plan' : 'Subscribe'}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
        {plans.length === 0 && !loadingPlans && (
            <Grid item xs={12}>
                <Text variant="muted">No subscription plans available.</Text>
            </Grid>
        )}
      </Grid>
            </Stack>

            {/* --- Credit Bundles Section --- */}
            <Stack spacing={2}>
                <Heading level="h2" size="2xl">Purchase Credits (Bundles)</Heading>
       {loadingBundles && <CircularProgress size={24} />}
      <Grid container spacing={3} sx={{ mb: 5 }}>
        {bundles.map((bundle) => (
          <Grid item xs={12} sm={6} md={4} key={bundle.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" component="div" gutterBottom>{bundle.name}</Typography>
                 <Typography variant="h5" sx={{ my: 1 }}>{bundle.credits_awarded} <Typography variant="caption" component="span">Credits</Typography></Typography>
                 {bundle.fast_credits_awarded > 0 && (
                    <Typography variant="body2" color="info.main" sx={{ mb: 1 }}>+ {bundle.fast_credits_awarded} Fast Credits</Typography>
                 )}
                 <Typography variant="body1" sx={{ mb: 2 }}>One-time purchase: ${bundle.price?.toFixed(2)}</Typography>
                 <Typography variant="body2" color="text.secondary" sx={{ minHeight: '40px' }}>{bundle.description}</Typography>
              </CardContent>
              <CardActions sx={{ px: 2, pb: 2, mt: 'auto' }}>
                <Button
                    variant="contained"
                    color="secondary"
                    fullWidth
                    disabled={!bundle.provider_price_id || walletLoading}
                    onClick={() => handleCreateCheckout(bundle.provider_price_id, 'bundle')}
                >
                  Purchase for ${bundle.price?.toFixed(2)}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
         {bundles.length === 0 && !loadingBundles && (
             <Grid item xs={12}>
                 <Text variant="muted">No credit bundles available.</Text>
             </Grid>
         )}
      </Grid>
            </Stack>

            {/* --- Credit Transaction History --- */}
            <Stack spacing={2}>
                <Heading level="h2" size="2xl">Credit History</Heading>
      {/* Show loader when fetching transactions */}
      {transactionsLoading && <CircularProgress size={24} />}
      {transactionsError && (
        <Alert severity="error">Error loading credit history: {transactionsError}</Alert>
      )}
      <TableContainer component={Paper} sx={{ mt: 2, mb: 3 }}>
        <Table stickyHeader aria-label="Credit transaction history table">
          <TableHead>
            <TableRow>
              <TableCell>Date</TableCell>
              <TableCell>Description</TableCell>
              <TableCell align="right">Amount</TableCell>
              <TableCell align="right">Balance After</TableCell>
              <TableCell align="right">Fast Bal. After</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedTransactions.map((tx) => (
              <TableRow hover key={tx.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                <TableCell component="th" scope="row">
                  {new Date(tx.created_at).toLocaleString()}
                </TableCell>
                <TableCell>{tx.reason?.replace(/_/g, ' ') || 'N/A'}{tx.notes ? ` (${tx.notes})` : ''}</TableCell>
                <TableCell align="right" sx={{ color: tx.change_amount > 0 ? 'success.main' : 'error.main', fontWeight: 'medium' }}>
                    {tx.change_amount > 0 ? '+' : ''}{tx.change_amount} ({tx.deducted_from || 'general'})
                </TableCell>
                 <TableCell align="right">{tx.balance_after ?? '-'}</TableCell>
                 <TableCell align="right">{tx.fast_balance_after ?? '-'}</TableCell>
              </TableRow>
            ))}
            {creditTransactions.length === 0 && !transactionsLoading && (
              <TableRow>
                <TableCell colSpan={5} align="center">No credit history found.</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {/* --- History Pagination --- */}
       {pageCount > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
            <Pagination
              count={pageCount}
              page={page}
              onChange={handlePageChange}
              color="primary"
              disabled={transactionsLoading} // Disable pagination while loading
            />
          </Box>
        )}
            </Stack>
        </Stack>
    </Container>
  );
}

export default MyPlan;
