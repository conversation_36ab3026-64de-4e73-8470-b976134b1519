// src/pages/Img2Video.jsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
    Container, Typography, TextField, Button, Box, CircularProgress, Grid,
    Paper, Avatar, Snackbar, Alert as MuiAlert, Fade, Card, CardMedia, CardContent, Tooltip
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
// Import Nhost hooks
import { useAuthenticationStatus, useUserId } from '@nhost/react';

// Custom Services & Hooks
import { GenerationService } from '../services/generationService';
import { CreditService } from '../services/creditService';
import { useStopwatch } from '../hooks/useStopwatch';

import UploadFileIcon from '@mui/icons-material/UploadFile';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import AIGenerationLoader from '../components/AIGenerationLoader';
import <PERSON><PERSON>extField from '../components/AnimatedTextField';

// Use Alert inside Snackbar for styling
const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

// --- Configuration ---
const TOOL_ID = 'img2video';
const INPUT_IMAGE_NODE_ID = '96'; // Node ID for the input image
const INPUT_PROMPT_NODE_ID = '6'; // Node ID for the text prompt
const PROMPT_INPUT_KEY = 'text'; // Input key within the prompt node

function Img2Video() {
    const theme = useTheme();
    // --- Nhost Hooks ---
    const { isLoading: isLoadingAuth, isAuthenticated } = useAuthenticationStatus();
    const userId = useUserId(); // Get current user ID

    // --- State ---
    const [file, setFile] = useState(null);
    const [previewUrl, setPreviewUrl] = useState(null);
    const [prompt, setPrompt] = useState(''); // Prompt for motion/style
    const [outputUrl, setOutputUrl] = useState('');
    const [loading, setLoading] = useState(false); // For the generation process
    const [outputObjectForSave, setOutputObjectForSave] = useState(null);
    const [saveInProgress, setSaveInProgress] = useState(false);
    const [error, setError] = useState(null); // For displaying generation errors
    const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });
    const [estimatedCost, setEstimatedCost] = useState(0);
    const costRateRef = useRef(0);
    const currentProjectIdRef = useRef(null);

    // --- Stopwatch Hook ---
    const handleStopwatchTick = useCallback((elapsedMs) => {
        if (costRateRef.current > 0) {
            const elapsedSeconds = elapsedMs / 1000;
            const cost = Math.ceil(elapsedSeconds * costRateRef.current);
            setEstimatedCost(cost);
        }
    }, []);
    const {
        formattedTime, isRunning: isStopwatchRunning, finalTime,
        start: startStopwatch, stop: stopStopwatch, reset: resetStopwatch
    } = useStopwatch({ onTick: handleStopwatchTick, tickInterval: 3000 });

    // --- Toast Handlers --- (Keep as before)
    const handleCloseToast = (event, reason) => {
      if (reason === 'clickaway') { return; }
      setToast(prev => ({ ...prev, open: false }));
    };
    const showToast = useCallback((message, severity = 'info') => {
        setToast({ open: true, message, severity });
    }, []);

    // --- Preview Effect --- (Keep as before)
    useEffect(() => {
        let o=null; if(file)o=URL.createObjectURL(file); setPreviewUrl(o); return ()=>{if(o) URL.revokeObjectURL(o);};
    }, [file]);

    // --- File Change Handler ---
    const handleFileChange = (e) => {
        const selectedFile = e.target.files?.[0] || null;
        setFile(selectedFile);
        setOutputUrl(''); setOutputObjectForSave(null); setError(null);
        resetStopwatch(); setEstimatedCost(0); costRateRef.current = 0;
        setLoading(false); setSaveInProgress(false); currentProjectIdRef.current = null;
        if (selectedFile) showToast('Video file selected. Ready.', 'info');
    };

    // === handleSubmit using updated GenerationService ===
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!isAuthenticated || !userId || !file || loading) return;
        setLoading(true); showToast('Initiating video generation...', 'info');
        setOutputUrl(''); setOutputObjectForSave(null); setError(null);
        resetStopwatch(); setEstimatedCost(0); costRateRef.current = 0;
        currentProjectIdRef.current = null;
        startStopwatch();
        let jobResult = null; let finalCost = 0;
        try {
            jobResult = await GenerationService.runGenerationJob(
                userId, TOOL_ID, file, prompt,
                INPUT_IMAGE_NODE_ID, INPUT_PROMPT_NODE_ID, PROMPT_INPUT_KEY
            );
            costRateRef.current = jobResult.costRatePerSecond;
            stopStopwatch();
            finalCost = jobResult.calculatedCost;
            console.log(`[Img2Video] Generation completed. Cost: ${finalCost} credits. PromptLogId: ${jobResult.promptLogId}`);
            setOutputUrl(jobResult.outputResult.r2PublicUrl);
            // --- Prepare complete data for saving (local and potentially cloud) ---
            const finalOutputData = {
                promptId: jobResult.promptLogId, // Use the ID returned from logging
                outputUrl: jobResult.outputResult.r2PublicUrl,
                outputR2Key: jobResult.outputResult.r2OutputKey, // Needed if saving to cloud later
                toolId: TOOL_ID,
                userId,
                timestamp: new Date().toISOString(), // Use for local sorting
                originalFileName: file.name,
                promptUsed: prompt.trim() || 'Animate this image',
                contentType: jobResult.outputResult.contentType,
                comfyOutputFileName: jobResult.outputResult.comfyOutputFileName,
                outputType: 'video', // Ensure this matches output
                credit_cost: finalCost,
                generation_duration_ms: Math.round(jobResult.durationMs),
                server_used_type: jobResult.serverTypeUsed, // Ensure this is included
                deducted_from: jobResult.deductedFrom, // Ensure this is included
                // Add a flag to distinguish locally saved items
                status: 'local', // Indicate it's not saved to DB yet
            };

            setOutputObjectForSave(finalOutputData); // Keep for the immediate "Save" button
            showToast(`Video Ready! (${finalCost} credits used)`, 'success');

            // --- ADD: Save to Local Storage for Recovery ---
            if (finalOutputData.promptId) {
                try {
                    localStorage.setItem(`output-${finalOutputData.promptId}`, JSON.stringify(finalOutputData));
                    console.log(`[Img2Video] Saved generation details locally for promptId: ${finalOutputData.promptId}`);
                } catch (storageError) {
                    console.error("[Img2Video] Error saving to localStorage:", storageError);
                    // Optionally show a non-blocking warning toast
                }
            } else {
                 console.warn("[Img2Video] Cannot save to local storage without a promptId.");
            }
            // --- End ADD ---

        } catch (err) {
            stopStopwatch();
            console.error(`[Img2Video] Generation Failed:`, err);
            const message = (err instanceof Error) ? err.message : "An unknown error occurred.";
            const displayError = message.startsWith('ComfyUI Error:') ? 'Generation failed internally. Please try again.' : message;
            showToast(displayError, 'error');
            setError(displayError);
        } finally {
            setLoading(false);
        }
    };

    // === handleSaveProject using updated GenerationService ===
    const handleSaveProject = async () => {
        if (!outputObjectForSave || saveInProgress || !outputUrl) return;
        setSaveInProgress(true);
        // Use the existing showToast for feedback
        showToast('Saving project...', 'info');
        try {
            const dataToSave = { ...outputObjectForSave };
            // Ensure all required fields are present in dataToSave, especially server_used_type
            // This should be populated correctly from runGenerationJob's return

            delete dataToSave.status; // Remove local-specific flags if any
            delete dataToSave.timestamp; // DB uses created_at/saved_at

            // Add userId from Nhost for the saveProjectMetadata call
            // Note: saveProjectMetadata in generationService will remove it before GraphQL call
            // as Hasura sets it. But it's good for the service function to receive it for validation.
            dataToSave.userId = userId;

            const savedProjectId = await GenerationService.saveProjectMetadata(dataToSave);
            currentProjectIdRef.current = savedProjectId;

            if (outputObjectForSave.promptId) {
                try {
                    localStorage.removeItem(`output-${outputObjectForSave.promptId}`);
                } catch (e) { console.error("Error removing from localStorage:", e); }
            }

            showToast('Project saved successfully!', 'success');
            setOutputObjectForSave(null); // Disable button

            // Credit Deduction (as before)
            const costToDeduct = dataToSave.credit_cost;
            if (costToDeduct > 0 && savedProjectId) {
                // ... (credit deduction logic) ...
                CreditService.requestCreditDeduction( /* ... */ )
                    .then(res => { if (!res.success) showToast(res.message, 'warning');})
                    .catch(err => showToast(err.message, 'error'));
            }

        } catch (err) {
             console.error('[Img2Video] Save project failed:', err);
             // err.message should be user-friendly from GenerationServiceError
             showToast(err.message || 'Failed to save project. Please try again.', 'error');
        }
        finally { setSaveInProgress(false); }
    };

    // --- UI Rendering ---
    if (isLoadingAuth) {
        return (<Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}><CircularProgress /></Box>);
    }

    return (
        <Container
            maxWidth={false}
            sx={{
                py: { xs: 2, sm: 3, md: 4 },
                px: { xs: 2, sm: 3, md: 4 },
                backgroundColor: theme.palette.background.default,
            }}
        >
             <Box sx={{ mb: 4 }}>
                <Typography variant="h1" component="h1">Image to Video Tool</Typography>
                <Typography variant="body1" color="text.secondary">Upload image, optional motion prompt, output video securely.</Typography>
             </Box>

            <Snackbar open={toast.open} autoHideDuration={4000} onClose={handleCloseToast} anchorOrigin={{ vertical: 'top', horizontal: 'right' }} TransitionComponent={Fade} sx={{ mt: '64px' }}>
                <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%', borderRadius: theme.shape.borderRadius }}>{toast.message}</Alert>
            </Snackbar>

            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: theme.spacing(3) }}>
                {/* Column 1: Input Settings */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                    <Typography variant="h2" component="h3" sx={{ mb: 2 }}>Input Settings</Typography>
                    <Paper component="form" onSubmit={handleSubmit} elevation={0} sx={{ p: theme.spacing(3), borderRadius: '16px', border: `1px solid ${theme.palette.divider}`, bgcolor: 'background.paper', flexGrow: 1, display: 'flex', flexDirection: 'column', gap: theme.spacing(2.5) }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                            <Button variant="outlined" component="label" startIcon={<UploadFileIcon />} disabled={!isAuthenticated || loading} fullWidth={!previewUrl} sx={{ flexShrink: 0 }}>Choose Image<input type="file" onChange={handleFileChange} hidden accept="image/png, image/jpeg, image/webp" required /></Button>
                            {previewUrl && <Avatar variant="rounded" src={previewUrl} alt="Preview" sx={{ width: 70, height: 70, border: `1px solid ${theme.palette.divider}`, borderRadius: theme.shape.borderRadius }} />}
                        </Box>
                        {file && <Typography variant="caption" display="block" sx={{ wordBreak: 'break-all', color: 'text.secondary' }}>{file.name}</Typography>}

                        {/* Standard TextField for prompt */}
                        <AnimatedTextField
                            label="Motion Prompt (Optional)"
                            multiline
                            rows={4}
                            fullWidth
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            disabled={!isAuthenticated || loading}
                            placeholder="Describe motion, style, e.g., 'gentle zoom in', 'camera pans right'"
                        />
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                            <Button type="submit" variant="contained" color="primary" disabled={!isAuthenticated || !file || loading}>
                                {loading ? <CircularProgress size={24} color="inherit"/> : 'Generate Video'}
                            </Button>
                            {/* Stopwatch Display */}
                            <Fade in={loading || (finalTime !== null && (outputUrl || error))} timeout={300}>
                                <Paper
                                    variant="outlined"
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 1,
                                        px: 1.5,
                                        py: 0.5,
                                        borderRadius: 1
                                    }}
                                >
                                    <AccessTimeIcon fontSize="small" sx={{ color: 'text.secondary' }}/>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            fontFamily: 'monospace',
                                            minWidth: '100px',
                                            color: isStopwatchRunning ? theme.palette.info.main : theme.palette.text.secondary
                                        }}
                                    >
                                        {formattedTime}
                                    </Typography>
                                    {isStopwatchRunning && costRateRef.current > 0 && (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 0.5,
                                                ml: 1,
                                                borderLeft: `1px solid ${theme.palette.divider}`,
                                                pl: 1
                                            }}
                                        >
                                            <CreditScoreIcon fontSize="small" sx={{ color: 'text.secondary' }}/>
                                            <Typography
                                                variant="caption"
                                                sx={{
                                                    fontFamily: 'monospace',
                                                    color: 'text.secondary'
                                                }}
                                            >
                                                ~{estimatedCost}cr
                                            </Typography>
                                        </Box>
                                    )}
                                </Paper>
                            </Fade>
                        </Box>
                    </Paper>
                </Box>

                {/* Column 2: Output Area */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                     <Typography variant="h2" component="h3" sx={{ mb: 2 }}> {loading ? 'Generating Video...' : outputUrl ? 'Output Video' : 'Output Area'} </Typography>
                     <Paper elevation={0} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: '300px', flexGrow: 1, borderRadius: '16px', border: `1px solid ${theme.palette.divider}`, bgcolor: 'background.paper', p: loading ? 0 : 2, overflow: 'hidden', boxSizing: 'border-box' }}>
                        {loading ? (
                           <Box sx={{width: '100%', height: '100%', display:'flex', alignItems:'center', justifyContent:'center'}}><AIGenerationLoader /></Box>
                        ) : outputUrl ? (
                           <Card elevation={0} sx={{width: '100%', height:'100%', boxShadow:'none', border:'none', background:'transparent', display:'flex', flexDirection:'column'}}>
                                <Box sx={{ flexGrow: 1, display: 'flex', alignItems:'center', justifyContent:'center', width: '100%', bgcolor: '#000' /* Dark BG for video */ }}>
                                    <CardMedia
                                        component="video" // Use video component
                                        src={outputUrl}
                                        controls // Add video controls
                                        sx={{ display: 'block', width: 'auto', height: 'auto', maxHeight: '100%', maxWidth: '100%', objectFit: 'contain' }}
                                    />
                                </Box>
                                <CardContent sx={{ textAlign: 'center', pt: 2, pb:0 }}>
                                    <Button variant="contained" color="secondary" onClick={handleSaveProject} disabled={!outputObjectForSave || saveInProgress}> {saveInProgress? 'Saving...' : 'Save to My Projects'} </Button>
                                </CardContent>
                           </Card>
                        ) : error ? (
                             <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, textAlign: 'center', p: 3 }}>
                                 <ErrorOutlineIcon sx={{ fontSize: 48, color: 'error.main' }} />
                                 <Typography variant="h6" color="error.main">Video Generation Failed</Typography>
                                 <Typography variant="body2" color="text.secondary">{error}</Typography>
                                 <Button variant="outlined" onClick={() => setError(null)}>Try Again</Button>
                             </Box>
                        ) : (
                             <Typography color="text.secondary" sx={{textAlign:'center'}}>Output video will appear here</Typography>
                        )}
                    </Paper>
                </Box>
            </Box>
        </Container>
    );
}
export default Img2Video;
