// src/components/WalletBalance.jsx
import React from 'react';
import {
  Paper,
  Stack,
  Chip,
  Button,
  Divider,
  Box,
  LinearProgress
} from '@mui/material';

// Design System Components
import {
  Text,
  Badge
} from './design-system/index';

// Icons
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import SpeedIcon from '@mui/icons-material/Speed';
import HistoryIcon from '@mui/icons-material/History';

/**
 * WalletBalance Component
 * Displays user's credit balance and daily credit claiming functionality
 */
function WalletBalance({ 
  walletInfo, 
  walletLoading, 
  walletError, 
  isClaimingCredits, 
  onClaimDailyCredits 
}) {
  if (walletLoading) {
    return (
      <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
        <Box sx={{ width: '100%', my: 2 }}>
          <LinearProgress />
          <Text size="sm" variant="muted" sx={{ mt: 1 }}>Loading wallet information...</Text>
        </Box>
      </Paper>
    );
  }

  if (walletError) {
    return (
      <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'error.main', borderRadius: 2 }}>
        <Text size="sm" color="error">
          Error loading wallet: {walletError}
        </Text>
      </Paper>
    );
  }

  if (!walletInfo) {
    return (
      <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
        <Text size="sm" variant="muted">No wallet information available.</Text>
      </Paper>
    );
  }

  return (
    <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2, height: '100%' }}>
      <Stack spacing={2}>
        {/* Credits Section */}
        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Text size="md" weight={600}>Regular Credits</Text>
            <Chip 
              icon={<AccountBalanceWalletIcon />} 
              label={walletInfo.credits_balance || walletInfo.credits || 0} 
              color="primary" 
              variant="outlined"
            />
          </Stack>
          
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Text size="md" weight={600}>Fast Credits</Text>
            <Chip 
              icon={<SpeedIcon />} 
              label={walletInfo.fast_credits_balance || walletInfo.fastCredits || 0} 
              color="secondary" 
              variant="outlined"
            />
          </Stack>

          {walletInfo.hasUnlimitedUsage && (
            <Box sx={{ mt: 1 }}>
              <Badge color="success" variant="filled">
                Unlimited Generation Active
              </Badge>
            </Box>
          )}
        </Stack>
        
        <Divider />
        
        {/* Daily Credits Section */}
        {walletInfo.canClaimDailyCredits ? (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddCircleOutlineIcon />}
            onClick={onClaimDailyCredits}
            disabled={isClaimingCredits}
            fullWidth
          >
            {isClaimingCredits ? 'Claiming...' : 'Claim Daily Credits'}
          </Button>
        ) : (
          <Button
            variant="outlined"
            startIcon={<HistoryIcon />}
            disabled
            fullWidth
          >
            Daily Credits Already Claimed
          </Button>
        )}
      </Stack>
    </Paper>
  );
}

export default WalletBalance;
