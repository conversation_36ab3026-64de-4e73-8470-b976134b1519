// src/pages/Dashboard.jsx
import React, { useState, useCallback, useEffect, Suspense } from 'react';
import {
  CircularProgress,
  Snackbar,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  Divider,
  Box,
  LinearProgress,
  Chip,
  Typography,
  Skeleton,
  Link
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuthenticationStatus, useUserData as useBasicUserData } from '@nhost/react';
import Projects from '../components/Projects';

// New separated components
import WalletBalance from '../components/WalletBalance';
import UsageStatistics from '../components/UsageStatistics';

// Design System Components
import {
  Container,
  Stack,
  Heading,
  Text,
  Alert,
  Badge
} from '../components/design-system/index';

// Services
import { CreditService } from '../services/creditService';
import { GenerationService } from '../services/generationService';
import { PassService } from '../services/passService';

// Icons
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import SpeedIcon from '@mui/icons-material/Speed';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import HistoryIcon from '@mui/icons-material/History';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';

// --- MUI Alert component ---
const MuiAlert = React.forwardRef(function MuiAlert(props, ref) {
  return <Alert {...props} ref={ref} variant="filled" elevation={6}/>;
});

// Simple PassBadge component
const PassBadge = ({ passType, expiresAt, hoursUsed, hoursTotal }) => {
  if (!passType) return null;
  
  return (
    <Box sx={{ 
      border: '1px solid', 
      borderColor: 'primary.main', 
      borderRadius: 2, 
      p: 2, 
      display: 'flex', 
      flexDirection: 'column',
      width: '100%',
      mb: 2
    }}>
      <Stack direction="row" spacing={1} alignItems="center">
        <CardMembershipIcon color="primary" />
        <Typography variant="h6" component="div">
          {passType.name}
        </Typography>
        <Badge color="primary" variant="filled">
          {passType.tier}
        </Badge>
      </Stack>
      
      {expiresAt && (
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Expires: {new Date(expiresAt).toLocaleDateString()}
        </Typography>
      )}
      
      {hoursTotal && (
        <Box sx={{ mt: 1 }}>
          <Typography variant="body2" gutterBottom>
            Hours Used: {hoursUsed || 0}/{hoursTotal}
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={Math.min(((hoursUsed || 0) / hoursTotal) * 100, 100)} 
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>
      )}
      
      {passType.is_unlimited && (
        <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold', color: 'success.main' }}>
          Unlimited Generations
        </Typography>
      )}
    </Box>
  );
};

function Dashboard() {
  const { isLoading: isLoadingAuth, isAuthenticated } = useAuthenticationStatus();
  const basicUserData = useBasicUserData();
  const userId = basicUserData?.id;

  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });
  const [savingLocalId, setSavingLocalId] = useState(null); // Track which local project is being saved
  const [walletInfo, setWalletInfo] = useState(null);
  const [walletLoading, setWalletLoading] = useState(false);
  const [walletError, setWalletError] = useState(null);
  const [isClaimingCredits, setIsClaimingCredits] = useState(false);
  const [passInfo, setPassInfo] = useState(null);
  const [isLoadingPass, setIsLoadingPass] = useState(false);
  const [passError, setPassError] = useState(null);

  const handleCloseToast = useCallback(() => { 
    setToast(prev => ({ ...prev, open: false })); 
  }, []);
  
  const showToast = useCallback((message, severity = 'info') => { 
    setToast({ open: true, message, severity }); 
  }, []);

  // Fetch wallet information using the updated CreditService
  const fetchWalletInfo = useCallback(async () => {
    if (!userId || !isAuthenticated) return;
    
    setWalletLoading(true);
    setWalletError(null);

    try {
      console.log('[Dashboard] Fetching wallet info...');
      const walletData = await CreditService.getUserWalletInfo(userId);
      console.log('[Dashboard] Wallet info retrieved:', walletData);
      setWalletInfo(walletData);
    } catch (error) {
      console.error('[Dashboard] Failed to get wallet info:', error);
      setWalletError(error.message);
    } finally {
      setWalletLoading(false);
    }
  }, [userId, isAuthenticated]);

  // Fetch pass info
  const fetchPassInfo = useCallback(async () => {
    if (!userId) return;
    
    setIsLoadingPass(true);
    setPassError(null);
    
    try {
      console.log('[Dashboard] Fetching pass info...');
      const passData = await PassService.getUserActivePass(userId);
      console.log('[Dashboard] Pass info retrieved:', passData);
      setPassInfo(passData);
    } catch (error) {
      console.error('[Dashboard] Failed to fetch pass info:', error);
      setPassError(error.message);
      // Don't show toast for this error to avoid UI clutter
    } finally {
      setIsLoadingPass(false);
    }
  }, [userId]);

  // Fetch wallet info and pass info on component mount and when userId changes
  useEffect(() => {
    if (userId && isAuthenticated) {
      fetchWalletInfo();
      fetchPassInfo();
    }
  }, [userId, isAuthenticated, fetchWalletInfo, fetchPassInfo]);

  // Handler for claiming daily credits
  const handleClaimDailyCredits = async () => {
    if (!userId || isClaimingCredits) return;
    
    setIsClaimingCredits(true);
    
    try {
      const result = await CreditService.claimDailyCredits(userId);
      showToast(`${result.creditsAdded} daily credits added successfully!`, 'success');
      // Refresh wallet info to show updated balance
      fetchWalletInfo();
    } catch (error) {
      console.error('[Dashboard] Failed to claim daily credits:', error);
      showToast(error.message || 'Failed to claim daily credits', 'error');
    } finally {
      setIsClaimingCredits(false);
    }
  };

  // Handler for saving a local project, passed to Projects component
  const handleSaveLocalProjectFromDashboard = async (localProjectFullData, localKey) => {
    if (!userId || savingLocalId) return;
    setSavingLocalId(localKey);
    showToast('Saving to cloud...', 'info');

    try {
      // Prepare data for the refactored saveProjectMetadata
      const projectDataToSave = {
        userId: userId, // generationService will use this for context, not for GQL insert
        promptId: localProjectFullData.promptId,
        inputFileUrl: localProjectFullData.nhostInputFile?.nhostFileUrl || localProjectFullData.inputFileUrl,
        promptInputText: localProjectFullData.userPromptText || localProjectFullData.promptUsed,
        creditCost: localProjectFullData.usageDetails?.calculatedCost ?? localProjectFullData.credit_cost ?? 0,
        generationDurationMs: localProjectFullData.usageDetails?.durationMs ?? localProjectFullData.generation_duration_ms ?? 0,
        outputs: localProjectFullData.generationOutput?.outputs || localProjectFullData.outputs || [],
        // Add any other necessary fields that saveProjectMetadata expects from localProjectFullData
      };

      const savedProjectId = await GenerationService.saveProjectMetadata(projectDataToSave);

      // Credit Deduction using updated deductCredits method
      const costToDeduct = projectDataToSave.creditCost;
      const serverTypeForDeduction = localProjectFullData.usageDetails?.serverTypeUsed === 'fast' ? 'fast' : 'regular';

      if (costToDeduct > 0 && savedProjectId) {
        await CreditService.deductCredits(
          costToDeduct,
          savedProjectId,
          serverTypeForDeduction
        );
        showToast('Project saved & credits deducted!', 'success');
      } else {
        showToast('Project saved successfully!', 'success');
      }

      localStorage.removeItem(localKey); // Remove from local storage
      // Refresh wallet info to show updated balance
      fetchWalletInfo();
    } catch (err) {
      console.error('[Dashboard] Failed to save local project:', err);
      showToast(err.message || 'Failed to save project.', 'error');
    } finally {
      setSavingLocalId(null);
    }
  };

  // Loading and authentication states
  if (isLoadingAuth || (isAuthenticated && !userId)) {
    return (
      <Container>
        <Stack justifyContent="center" alignItems="center" sx={{ height: '80vh' }}>
          <CircularProgress />
        </Stack>
      </Container>
    );
  }
  
  if (!isAuthenticated) {
    return (
      <Container sx={{ py: 4 }}>
        <Alert severity="warning">Please log in to view your dashboard.</Alert>
      </Container>
    );
  }

  const displayName = basicUserData?.displayName || 'User';

  return (
    <Container maxWidth="1280px" sx={{ py: { xs: 2, sm: 4 } }}>
      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleCloseToast}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <MuiAlert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%' }}>
          {toast.message}
        </MuiAlert>
      </Snackbar>

      <Stack spacing={4}>
        {/* Header Section with Welcome */}
        <Stack spacing={1}>
          <Heading level="h1" size="4xl">
            Welcome, {displayName}!
          </Heading>
          <Text size="lg" variant="muted">
            Manage your projects and credits
          </Text>
        </Stack>

        {/* Wallet & Usage Section */}
        <Card variant="outlined" sx={{ borderRadius: 2, overflow: 'hidden' }}>
          <Box sx={{ bgcolor: 'primary.main', py: 2, px: 3 }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <AccountBalanceWalletIcon sx={{ color: 'white' }} />
              <Heading level="h2" size="xl" sx={{ color: 'white', my: 0 }}>
                Wallet & Usage
              </Heading>
            </Stack>
          </Box>

          <CardContent>
            <Grid container spacing={3}>
              {/* Wallet Balance - Left Side */}
              <Grid item xs={12} md={6}>
                <WalletBalance
                  walletInfo={walletInfo}
                  walletLoading={walletLoading}
                  walletError={walletError}
                  isClaimingCredits={isClaimingCredits}
                  onClaimDailyCredits={handleClaimDailyCredits}
                />
              </Grid>

              {/* Usage Statistics - Right Side */}
              <Grid item xs={12} md={6}>
                <UsageStatistics
                  walletInfo={walletInfo}
                  passInfo={passInfo}
                  isLoading={walletLoading || isLoadingPass}
                />
              </Grid>
            </Grid>

            {/* Active Pass Section - Full Width Below */}
            {(passInfo?.hasActivePass && passInfo?.activePass) && (
              <Box sx={{ mt: 3 }}>
                <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <CardMembershipIcon color="primary" />
                    <Text size="md" weight={600}>Active Pass</Text>
                  </Stack>

                  {isLoadingPass ? (
                    <Box sx={{ width: '100%' }}>
                      <Skeleton variant="rectangular" height={100} />
                    </Box>
                  ) : passError ? (
                    <Alert severity="warning">
                      Error loading pass information: {passError}
                    </Alert>
                  ) : (
                    <PassBadge
                      passType={passInfo.activePass.pass_type}
                      expiresAt={passInfo.activePass.end_date}
                      hoursUsed={passInfo.activePass.hours_used}
                      hoursTotal={passInfo.activePass.pass_type?.hours_included}
                    />
                  )}
                </Paper>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Unsaved Generations Section */}
        <Stack spacing={2}>
          <Heading level="h2" size="2xl">
            Unsaved Generations
          </Heading>
          <Projects
            showUnsaved={true}
            useCarousel={true}
            onSaveLocalProject={handleSaveLocalProjectFromDashboard}
            onActionFeedback={showToast}
          />
        </Stack>

        {/* Saved Projects Section */}
        <Stack spacing={2}>
          <Heading level="h2" size="2xl">
            Saved Projects
          </Heading>
          <Projects
            showUnsaved={false}
            useCarousel={true}
            onActionFeedback={showToast}
          />
        </Stack>


      </Stack>
    </Container>
  );
}

export default Dashboard;
