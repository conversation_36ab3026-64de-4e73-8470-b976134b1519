// src/services/errors.js
export class GenerationServiceError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'GenerationServiceError';
    this.originalError = originalError;
  }
}

export class GraphQLServiceError extends Error {
  constructor(message, graphqlErrors = null, originalError = null) {
    super(message);
    this.name = 'GraphQLServiceError';
    this.graphqlErrors = graphqlErrors;
    this.originalError = originalError;
  }
}

export class CreditServiceError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'CreditServiceError';
    this.originalError = originalError;
  }
}

export class PassServiceError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'PassServiceError';
    this.originalError = originalError;
  }
}