// src/utils/validation.js
/**
 * Comprehensive validation utilities for authentication forms
 */

// Email validation regex - RFC 5322 compliant
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Password strength criteria
const PASSWORD_CRITERIA = {
  minLength: 6,
  hasUppercase: /[A-Z]/,
  hasLowercase: /[a-z]/,
  hasNumber: /\d/,
  hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/
};

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {object} Validation result with isValid and message
 */
export const validateEmail = (email) => {
  if (!email) {
    return { isValid: false, message: 'Email is required' };
  }
  
  if (!EMAIL_REGEX.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }
  
  return { isValid: true, message: '' };
};

/**
 * Validates password strength
 * @param {string} password - Password to validate
 * @returns {object} Validation result with isValid, message, and strength score
 */
export const validatePassword = (password) => {
  if (!password) {
    return { isValid: false, message: 'Password is required', strength: 0 };
  }
  
  if (password.length < PASSWORD_CRITERIA.minLength) {
    return { 
      isValid: false, 
      message: `Password must be at least ${PASSWORD_CRITERIA.minLength} characters long`,
      strength: 0
    };
  }
  
  // Calculate strength score
  let strength = 0;
  let strengthMessages = [];
  
  // Length check
  if (password.length >= 8) {
    strength += 25;
  } else if (password.length >= 6) {
    strength += 15;
    strengthMessages.push('Use 8+ characters for better security');
  }
  
  // Character type checks
  if (PASSWORD_CRITERIA.hasUppercase.test(password)) {
    strength += 20;
  } else {
    strengthMessages.push('Add uppercase letters');
  }
  
  if (PASSWORD_CRITERIA.hasLowercase.test(password)) {
    strength += 20;
  } else {
    strengthMessages.push('Add lowercase letters');
  }
  
  if (PASSWORD_CRITERIA.hasNumber.test(password)) {
    strength += 20;
  } else {
    strengthMessages.push('Add numbers');
  }
  
  if (PASSWORD_CRITERIA.hasSpecialChar.test(password)) {
    strength += 15;
  } else {
    strengthMessages.push('Add special characters');
  }
  
  // Determine if password is acceptable
  const isValid = strength >= 40; // Minimum acceptable strength
  
  return {
    isValid,
    message: isValid ? '' : `Weak password. ${strengthMessages.slice(0, 2).join(', ')}`,
    strength: Math.min(strength, 100),
    suggestions: strengthMessages
  };
};

/**
 * Validates username
 * @param {string} username - Username to validate
 * @returns {object} Validation result with isValid and message
 */
export const validateUsername = (username) => {
  if (!username || !username.trim()) {
    return { isValid: false, message: 'Username is required' };
  }
  
  const trimmed = username.trim();
  
  if (trimmed.length < 3) {
    return { isValid: false, message: 'Username must be at least 3 characters long' };
  }
  
  if (trimmed.length > 30) {
    return { isValid: false, message: 'Username must be less than 30 characters' };
  }
  
  // Check for valid characters (alphanumeric, underscore, hyphen)
  if (!/^[a-zA-Z0-9_-]+$/.test(trimmed)) {
    return { isValid: false, message: 'Username can only contain letters, numbers, underscores, and hyphens' };
  }
  
  return { isValid: true, message: '' };
};

/**
 * Validates password confirmation
 * @param {string} password - Original password
 * @param {string} confirmPassword - Password confirmation
 * @returns {object} Validation result with isValid and message
 */
export const validatePasswordConfirmation = (password, confirmPassword) => {
  if (!confirmPassword) {
    return { isValid: false, message: 'Please confirm your password' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, message: 'Passwords do not match' };
  }
  
  return { isValid: true, message: '' };
};

/**
 * Gets password strength color for UI display
 * @param {number} strength - Password strength score (0-100)
 * @returns {string} Material-UI color name
 */
export const getPasswordStrengthColor = (strength) => {
  if (strength < 40) return 'error';
  if (strength < 70) return 'warning';
  return 'success';
};

/**
 * Gets password strength label for UI display
 * @param {number} strength - Password strength score (0-100)
 * @returns {string} Strength label
 */
export const getPasswordStrengthLabel = (strength) => {
  if (strength < 40) return 'Weak';
  if (strength < 70) return 'Medium';
  return 'Strong';
};

/**
 * Validates entire login form
 * @param {object} formData - Form data object
 * @returns {object} Validation result with errors object and isValid flag
 */
export const validateLoginForm = (formData) => {
  const { email, password } = formData;
  const errors = {};
  
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    errors.email = emailValidation.message;
  }
  
  if (!password) {
    errors.password = 'Password is required';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Validates entire registration form
 * @param {object} formData - Form data object
 * @returns {object} Validation result with errors object and isValid flag
 */
export const validateRegistrationForm = (formData) => {
  const { username, email, password, confirmPassword, acceptTerms } = formData;
  const errors = {};
  
  const usernameValidation = validateUsername(username);
  if (!usernameValidation.isValid) {
    errors.username = usernameValidation.message;
  }
  
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    errors.email = emailValidation.message;
  }
  
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.message;
  }
  
  const confirmPasswordValidation = validatePasswordConfirmation(password, confirmPassword);
  if (!confirmPasswordValidation.isValid) {
    errors.confirmPassword = confirmPasswordValidation.message;
  }
  
  if (!acceptTerms) {
    errors.terms = 'You must accept the terms and conditions';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    passwordStrength: passwordValidation.strength || 0
  };
};

/**
 * Debounce function for validation
 * @param {function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Common error messages for authentication
 */
export const AUTH_ERROR_MESSAGES = {
  INVALID_CREDENTIALS: 'Invalid email or password. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your internet connection and try again.',
  RATE_LIMIT: 'Too many attempts. Please wait a moment before trying again.',
  EMAIL_NOT_VERIFIED: 'Please verify your email address before logging in.',
  EMAIL_ALREADY_EXISTS: 'An account with this email already exists. Try logging in instead.',
  WEAK_PASSWORD: 'Password is too weak. Please choose a stronger password.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  REGISTRATION_FAILED: 'Registration failed. Please try again.',
  LOGIN_FAILED: 'Login failed. Please try again.',
  VERIFICATION_FAILED: 'Email verification failed. Please try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
};

/**
 * Maps Nhost error codes to user-friendly messages
 * @param {object} error - Nhost error object
 * @returns {string} User-friendly error message
 */
export const mapNhostError = (error) => {
  if (!error) return AUTH_ERROR_MESSAGES.UNKNOWN_ERROR;
  
  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.error || error.code || '';
  
  // Check for specific error patterns
  if (errorCode === 'unverified-user' || errorMessage.includes('unverified') || errorMessage.includes('verification')) {
    return AUTH_ERROR_MESSAGES.EMAIL_NOT_VERIFIED;
  }
  
  if (errorMessage.includes('invalid credentials') || errorMessage.includes('invalid email or password')) {
    return AUTH_ERROR_MESSAGES.INVALID_CREDENTIALS;
  }
  
  if (errorMessage.includes('email already') || errorMessage.includes('already exists')) {
    return AUTH_ERROR_MESSAGES.EMAIL_ALREADY_EXISTS;
  }
  
  if (errorMessage.includes('password') && (errorMessage.includes('weak') || errorMessage.includes('short'))) {
    return AUTH_ERROR_MESSAGES.WEAK_PASSWORD;
  }
  
  if (errorMessage.includes('invalid email')) {
    return AUTH_ERROR_MESSAGES.INVALID_EMAIL;
  }
  
  if (errorMessage.includes('network') || errorMessage.includes('connection')) {
    return AUTH_ERROR_MESSAGES.NETWORK_ERROR;
  }
  
  if (errorMessage.includes('rate limit') || errorMessage.includes('too many')) {
    return AUTH_ERROR_MESSAGES.RATE_LIMIT;
  }
  
  // Return original message if no pattern matches
  return error.message || AUTH_ERROR_MESSAGES.UNKNOWN_ERROR;
};
